# 简单的图片同步脚本
$malay = Get-Content 'swapjob_guide_ms.html' -Raw
$chinese = Get-Content 'swapjob_guide.html' -Raw

# 提取所有图片
$malayImgs = [regex]::Matches($malay, '<img[^>]*src="data:image/[^"]+"[^>]*>')
$chineseImgs = [regex]::Matches($chinese, '<img[^>]*src="data:image/[^"]+"[^>]*>')

Write-Host "马来版本图片: $($malayImgs.Count)"
Write-Host "中文版本图片: $($chineseImgs.Count)"

if ($chineseImgs.Count -ge $malayImgs.Count) {
    Write-Host "无需同步"
    exit
}

# 找到中文版本最后一张图片位置
$lastImg = $chineseImgs[$chineseImgs.Count - 1].Value
$pos = $chinese.IndexOf($lastImg)
if ($pos -lt 0) {
    Write-Host "找不到最后一张图片位置"
    exit
}

$endPos = $chinese.IndexOf('>', $pos) + 1
$before = $chinese.Substring(0, $endPos)
$after = $chinese.Substring($endPos)

# 添加缺失的图片
$newContent = $before
for ($i = $chineseImgs.Count; $i -lt $malayImgs.Count; $i++) {
    $newContent += "`r`n    " + $malayImgs[$i].Value
}
$newContent += $after

# 保存文件
$newContent | Out-File 'swapjob_guide.html' -Encoding UTF8
Write-Host "同步完成！现在有 $($malayImgs.Count) 张图片"