#!/usr/bin/env python3
import re

# 读取base64.txt文件
with open('C:/Users/<USER>/Downloads/MicrosoftEdgeDropFiles/Default/Swap job/base64.txt', 'r', encoding='utf-8') as f:
    base64_content = f.read()

# 读取HTML文件
with open('C:/Users/<USER>/Downloads/MicrosoftEdgeDropFiles/Default/Swap job/swapjob_guide.html', 'r', encoding='utf-8') as f:
    html_content = f.read()

# 提取logo的base64数据
logo_match = re.search(r'logo:\s*// gmh logo\.png\s*<img src="([^"]+)"', base64_content)
if logo_match:
    logo_base64 = logo_match.group(1)
    print("Found logo base64 data")
    # 替换HTML中的logo
    html_content = html_content.replace('src="null"', f'src="{logo_base64}"', 1)

# 提取所有图片的base64数据
for i in range(1, 8):
    pattern = f'image_{i}:\s*// [^.]+\.jpg\s*<img src="([^"]+)"'
    image_match = re.search(pattern, base64_content)
    if image_match:
        image_base64 = image_match.group(1)
        print(f"找到图片{i}的base64数据")
        # 替换HTML中对应的图片
        html_content = html_content.replace('src="null"', f'src="{image_base64}"', 1)

# 写入更新后的HTML文件
with open('C:/Users/<USER>/Downloads/MicrosoftEdgeDropFiles/Default/Swap job/swapjob_guide.html', 'w', encoding='utf-8') as f:
    f.write(html_content)

print("base64数据替换完成")