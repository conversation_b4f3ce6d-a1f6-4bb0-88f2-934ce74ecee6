#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re

# 读取base64.txt文件
with open('C:/Users/<USER>/Downloads/MicrosoftEdgeDropFiles/Default/Swap job/base64.txt', 'r', encoding='utf-8') as f:
    base64_content = f.read()

# 提取logo的base64数据
logo_pattern = r'logo:\s*// gmh logo\.png\s*<img src="([^"]+)"'
logo_match = re.search(logo_pattern, base64_content)
if logo_match:
    logo_base64 = logo_match.group(1)
    print("Found logo base64 data, length:", len(logo_base64))

# 提取所有图片的base64数据 - 根据实际格式调整
image_patterns = [
    r'// 1\. Menu to add friend\.jpg\s*<img src="([^"]+)"',
    r'// 2\. Menu to show Friend and swap job quantity\.jpg\s*<img src="([^"]+)"',
    r'// 3\. Fill phone number to add friend\.jpg\s*<img src="([^"]+)"',
    r'// 4\. Slide name card to left to cancel friend list\.jpg\s*<img src="([^"]+)"',
    r'// 5\. Press to SWAP job\.jpg\s*<img src="([^"]+)"',
    r'// 6\. Menu of select friend to swap\.jpg\s*<img src="([^"]+)"',
    r'// 7\. Job SWAP by other\.jpg\s*<img src="([^"]+)"'
]

image_base64_list = []
for i, pattern in enumerate(image_patterns, 1):
    image_match = re.search(pattern, base64_content)
    if image_match:
        image_base64 = image_match.group(1)
        image_base64_list.append(image_base64)
        print(f"Found image {i} base64 data, length:", len(image_base64))
    else:
        print(f"Could not find image {i} base64 data")
        image_base64_list.append(None)

# 更新所有HTML文件
html_files = [
    'C:/Users/<USER>/Downloads/MicrosoftEdgeDropFiles/Default/Swap job/swapjob_guide.html',
    'C:/Users/<USER>/Downloads/MicrosoftEdgeDropFiles/Default/Swap job/swapjob_guide_en.html',
    'C:/Users/<USER>/Downloads/MicrosoftEdgeDropFiles/Default/Swap job/swapjob_guide_ms.html'
]

for html_file in html_files:
    print(f"\nProcessing {html_file}...")

    # 读取HTML文件
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()

    # 替换logo - 查找所有img标签并替换class="logo"的那个
    def replace_logo(match):
        return match.group(0).replace(match.group(1), logo_base64)

    logo_pattern_html = r'(<img[^>]*class="logo"[^>]*src=")([^"]+)("[^>]*>)'
    html_content = re.sub(logo_pattern_html, replace_logo, html_content)
    print("Logo replaced")

    # 替换其他图片 - 按顺序替换
    for i, image_base64 in enumerate(image_base64_list):
        if image_base64:
            # 查找第i+1个图片标签
            img_pattern = r'(<img[^>]*class="step-image"[^>]*src=")([^"]+)("[^>]*>)'
            def replace_image(match):
                nonlocal replacement_count
                replacement_count += 1
                if replacement_count == i + 1:
                    return match.group(0).replace(match.group(2), image_base64)
                return match.group(0)

            replacement_count = 0
            html_content = re.sub(img_pattern, replace_image, html_content)
            print(f"Image {i+1} replaced")

    # 写入更新后的HTML文件
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"Base64 replacement completed for {html_file}")

print("\nAll files updated successfully!")