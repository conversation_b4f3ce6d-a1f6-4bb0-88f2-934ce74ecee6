# 读取两个文件
$malayContent = Get-Content 'swapjob_guide_ms.html' -Raw
$chineseContent = Get-Content 'swapjob_guide.html' -Raw

# 使用正则表达式找到所有图片标签
$malayImages = [regex]::Matches($malayContent, '<img[^>]*src="data:image/[^"]+"[^>]*>')
$chineseImages = [regex]::Matches($chineseContent, '<img[^>]*src="data:image/[^"]+"[^>]*>')

Write-Host "马来版本图片数量: $($malayImages.Count)"
Write-Host "中文版本图片数量: $($chineseImages.Count)"

if ($chineseImages.Count -ge $malayImages.Count) {
    Write-Host "中文版本已经包含所有图片，无需同步。"
    exit
}

# 找到需要添加的图片
$imagesToAdd = @()
for ($i = $chineseImages.Count; $i -lt $malayImages.Count; $i++) {
    $imagesToAdd += $malayImages[$i].Value
    Write-Host "需要添加的图片 $i : $($malayImages[$i].Value.Substring(0, 100))..."
}

# 找到中文版本中最后一张图片的位置
$lastImageMatch = $chineseImages[$chineseImages.Count - 1]
$lastImageIndex = $chineseContent.IndexOf($lastImageMatch.Value)

if ($lastImageIndex -lt 0) {
    Write-Host "错误: 无法找到中文版本中的图片位置"
    exit
}

# 在最后一张图片后添加缺失的图片
$imgEndIndex = $chineseContent.IndexOf('>', $lastImageIndex) + 1
$beforeContent = $chineseContent.Substring(0, $imgEndIndex)
$afterContent = $chineseContent.Substring($imgEndIndex)

# 构建新的内容
$newContent = $beforeContent + [Environment]::NewLine + [Environment]::NewLine
foreach ($img in $imagesToAdd) {
    $newContent += "    " + $img + [Environment]::NewLine
}
$newContent += $afterContent

# 写入文件
$newContent | Out-File -FilePath 'swapjob_guide.html' -Encoding UTF8
Write-Host "图片同步完成！现在中文版本包含 $($malayImages.Count) 张图片。"