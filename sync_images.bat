@echo off
setlocal enabledelayedexpansion

echo 正在同步swapjob_guide.html的base64图片...

:: 创建临时文件来存储图片数据
echo 提取马来版本中的图片数据...
findstr /n "data:image" "swapjob_guide_ms.html" > temp_malay_images.txt
findstr /n "data:image" "swapjob_guide.html" > temp_chinese_images.txt

:: 比较两个文件，确定需要添加的图片
echo 分析需要同步的图片...
for /f "tokens=1 delims=:" %%a in ('type temp_malay_images.txt ^| find /c /v ""') do set malay_count=%%a
for /f "tokens=1 delims=:" %%a in ('type temp_chinese_images.txt ^| find /c /v ""') do set chinese_count=%%a

echo 马来版本图片数量: %malay_count%
echo 中文版本图片数量: %chinese_count%

if %chinese_count% geq %malay_count% (
    echo 中文版本已经包含所有图片，无需同步。
    goto cleanup
)

:: 使用PowerShell来处理文件同步
echo 使用PowerShell进行精确同步...
powershell -ExecutionPolicy Bypass -Command "
# 读取两个文件
$malayContent = Get-Content 'swapjob_guide_ms.html' -Raw
$chineseContent = Get-Content 'swapjob_guide.html' -Raw

# 使用正则表达式找到所有图片标签
$malayImages = [regex]::Matches($malayContent, '<img[^>]*src=\"data:image/[^\"]+\"[^>]*>')
$chineseImages = [regex]::Matches($chineseContent, '<img[^>]*src=\"data:image/[^\"]+\"[^>]*>')

Write-Host \"马来版本图片数量: $($malayImages.Count)\"
Write-Host \"中文版本图片数量: $($chineseImages.Count)\"

if ($chineseImages.Count -ge $malayImages.Count) {
    Write-Host \"中文版本已经包含所有图片，无需同步。\"
    exit
}

# 找到需要添加的图片位置
# 首先找到中文版本中最后一张图片的位置
$lastImageIndex = -1
for ($i = $chineseImages.Count - 1; $i -ge 0; $i--) {
    $lastImageIndex = $chineseContent.LastIndexOf($chineseImages[$i].Value, $lastImageIndex)
    if ($lastImageIndex -ge 0) { break }
}

if ($lastImageIndex -lt 0) {
    Write-Host \"错误: 无法找到中文版本中的图片位置\"
    exit
}

# 在最后一张图片后添加缺失的图片
$insertPosition = $chineseContent.IndexOf('>', $lastImageIndex) + 1
$imagesToAdd = @()

for ($i = $chineseImages.Count; $i -lt $malayImages.Count; $i++) {
    $imagesToAdd += $malayImages[$i].Value
    Write-Host \"添加图片 $i : $($malayImages[$i].Value.Substring(0, 50))...\"
}

# 构建新的内容
$newContent = $chineseContent.Substring(0, $insertPosition) + [Environment]::NewLine + [Environment]::NewLine
foreach ($img in $imagesToAdd) {
    $newContent += \"    \" + $img + [Environment]::NewLine
}
$newContent += $chineseContent.Substring($insertPosition)

# 写入文件
$newContent | Out-File -FilePath 'swapjob_guide.html' -Encoding UTF8
Write-Host \"图片同步完成！\"
"

echo 同步完成！

:cleanup
:: 清理临时文件
if exist temp_malay_images.txt del temp_malay_images.txt
if exist temp_chinese_images.txt del temp_chinese_images.txt

echo 验证同步结果...
findstr /n "data:image" "swapjob_guide.html" | find /c /v ""
pause